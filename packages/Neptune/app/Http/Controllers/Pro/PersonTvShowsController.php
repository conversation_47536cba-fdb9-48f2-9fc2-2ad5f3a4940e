<?php

namespace Packages\Neptune\app\Http\Controllers\Pro;

use App\Exceptions\Pro\ProException;
use App\Mail\ProPublishMessage;
use App\Models\FilmGenre;
use App\Models\Person;
use App\Models\PersonRoleTvShow;
use App\Models\TvChannel;
use App\Models\TvShow;
use App\Trak\Cache\InvalidatorPersonCache;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Mail;
use Packages\Neptune\app\Http\Requests\Pro\PersonTvShowsStoreRequest;
use Packages\Neptune\app\Http\Requests\Pro\PersonTvShowsUpdateRequest;

class PersonTvShowsController extends ProController
{

    public function __construct(InvalidatorPersonCache $cacheInvalidator)
    {
        parent::__construct($cacheInvalidator);

        // redirect to proper page if adminised
        $this->middleware(function ($request, $next) {
            // $this->logged_user is filled in in the ProController constructor
            if($this->logged_user->adminised)
            {
                return redirect()->route('neptune.pro.adminised.plays.index');
            }

            return $next($request);
        });
    }

    /**
     * Show the form for previewing person tv shows
     *
     * @param  $person_slug
     */
    public function index($person_slug)
    {
        // get the person
        try
        {
            $person = Person::where('slug', $person_slug)->with('mainImage')->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Person not found',
                ]
            ]);
        }

        // throws ProException
        $this->personChecks($person);

        $this->view_data['selected_person'] = $person;
        $this->view_data['tvShows']         = $this->logged_user->tvShowsCreated()->orderBy('created_at', 'desc')->paginate(10);

        return view('neptune::pro.tvShows.index', $this->view_data);
    }

    /**
     * Show the form for creating the pro person tv show
     *
     * @param  $person_slug
     */
    public function create($person_slug)
    {
        // get the person
        try
        {
            $person = Person::where('slug', $person_slug)->with('mainImage')->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Person not found',
                ]
            ]);
        }

        // throws ProException
        $this->personChecks($person);

        $this->view_data['selected_person'] = $person;

        return view('neptune::pro.tvShows.create', $this->view_data);
    }


    /**
     * Store the specified resource in storage.
     *
     * @param PersonTvShowsStoreRequest $request
     * @param $person_slug
     * @throws PersonProException
     */
    public function store(PersonTvShowsStoreRequest $request, $person_slug)
    {
        // get the person
        try
        {
            $person = Person::where('slug', $person_slug)->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Person not found',
                ]
            ]);
        }

        // throws ProException
        $this->personChecks($person);

        // Create TvShow with non-translatable fields first
        $tvShow = TvShow::create(['year' => trim($request->year)]);

        // Set translatable fields and save them first
        $tvShow->translateOrNew('el')->title = trim($request->title);
        $tvShow->save(); // This saves both the model and translations

        // Force regenerate slug now that translation is saved
        $tvShow->slug = null; // Clear existing slug
        $tvShow->save(); // Regenerate slug with proper title

        // ...set the user_id attribute
        $tvShow->user_id = $this->logged_user->id;

        // ...set the moderated attribute to false
        $tvShow->moderated = false;

        // ...set the finalised attribute to false
        $tvShow->finalised = false;

        // ...set the published attribute to false
        $tvShow->published = false;

        // ...and finaly save the model to persist the changes
        $tvShow->save();

        return redirect()->route('neptune.pro.tvShows.editInfo', [$person->slug, $tvShow->slug])
            ->with('success', trans('neptune::pro/tvShows.create.successful_creation'));
    }

    /**
     * Show the form for editing the pro person tvShow info
     *
     * @param  $person_slug
     * @param  $tvShow_slug
     * @throws PersonProException
     */
    public function editInfo($person_slug, $tvShow_slug)
    {
        // get the person
        try
        {
            $person = Person::where('slug', $person_slug)->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Person not found',
                ]
            ]);
        }

        // throws ProExcpetion
        $this->personChecks($person);

        // get the tvShow
        try
        {
            $tvShow = TvShow::where('slug', $tvShow_slug)
                ->firstOrFail()
                ->load('tvChannel')
                ->load('filmGenres');
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'TvShow was not found',
                ]
            ]);
        }

        // throws ProException
        $this->tvShowChecks($person, $tvShow);

        $this->view_data['selected_person'] = $person;
        $this->view_data['tvShow']          = $tvShow;
        $this->view_data['roles']           = [];
        $this->view_data['tvChannels']      = [];
        $this->view_data['filmGenres']      = [];

        // get all roles for tvShows
        foreach (config('roles')->where('for_tv', true) as $role_id => $role)
        {
            $this->view_data['roles'][$role_id] = $role->description;
        }

        // get all tv channels
        foreach(TvChannel::orderBy('name')->get() as $tvChannel)
        {
            $this->view_data['tvChannels'][$tvChannel->id] = $tvChannel->name;
        }

        // get all filmGenres
        foreach(FilmGenre::orderBy('name')->get() as $filmGenre)
        {
            $this->view_data['filmGenres'][$filmGenre->id] = $filmGenre->name;
        }

        // get role ids for the given tvShow for the given person
        $role_ids = PersonRoleTvShow::where('person_id', $person->id)
            ->where('tv_show_id', $tvShow->id)
            ->pluck('role_id');
        $this->view_data['selected_roles'] = $role_ids;

        return view('neptune::pro.tvShows.editInfo', $this->view_data);
    }

    /**
     * Show the form for editing the pro person tv show images
     *
     * @param  $person_slug
     * @param  $tvShow_slug
     * @throws PersonProException
     */
    public function editImages($person_slug, $tvShow_slug)
    {
        // get the person
        try
        {
            $person = Person::where('slug', $person_slug)->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Person not found',
                ]
            ]);
        }

        // throws ProExcpetion
        $this->personChecks($person);

        // get the tv show
        try
        {
            $tvShow = TvShow::where('slug', $tvShow_slug)->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Tv show was not found',
                ]
            ]);
        }

        // throws ProException
        $this->tvShowChecks($person, $tvShow);

        $this->view_data['selected_person'] = $person;
        $this->view_data['tvShow']          = $tvShow;

        return view('neptune::pro.tvShows.editImages', $this->view_data);
    }

    /**
     * Show the form for editing the pro tv show person roles
     *
     * @param  $person_slug
     * @param  $tvShow_slug
     * @throws PersonProException
     */
    public function editRoles($person_slug, $tvShow_slug)
    {
        // get the person
        try
        {
            $person = Person::where('slug', $person_slug)->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Person not found',
                ]
            ]);
        }

        // throws ProException
        $this->personChecks($person);

        // get the tvShow
        try
        {
            $tvShow = TvShow::where('slug', $tvShow_slug)
                ->with('roles')
                ->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'TV show was not found',
                ]
            ]);
        }

        // throws ProException
        $this->tvShowChecks($person, $tvShow);

        $tvShow->load([
            'roles.tvShowPeople' => function ($query) use ($tvShow) {
                $query->wherePivot('tv_show_id', '=', $tvShow->id);
            },
        ]);

        $rolesWithoutActors = [];
        // Workaround to get list of roles for which this tvShow
        // doesn't have any person
        $rolesIdsWithActors = [];
        foreach ($tvShow->roles as $tvShowRole)
        {
            $rolesIdsWithActors[] = $tvShowRole->id;
        }
        foreach (config('roles')->where('for_tv', true) as $role)
        {
            if ( ! in_array($role->id, $rolesIdsWithActors))
            {
                $rolesWithoutActors[] = $role;
            }
        }

        $this->view_data['selected_person']     = $person;
        $this->view_data['tvShow']              = $tvShow;
        $this->view_data['roles']               = config('roles')->where('for_tv', true)->sortBy('sort_order');
        $this->view_data['rolesWithoutActors']  = $rolesWithoutActors;

        return view('neptune::pro.tvShows.editRoles', $this->view_data);
    }

    /**
     * Show the form for editing the pro person tv show private note
     *
     * @param  $person_slug
     * @param  $tvShow_slug
     * @throws PersonProException
     */
    public function editNotes($person_slug, $tvShow_slug)
    {
        // get the person
        try
        {
            $person = Person::where('slug', $person_slug)->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Person not found',
                ]
            ]);
        }

        // throws ProExcpetion
        $this->personChecks($person);

        // get the tvShow
        try
        {
            $tvShow = TvShow::where('slug', $tvShow_slug)
                ->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Tv show was not found',
                ]
            ]);
        }

        // throws ProException
        $this->tvShowChecks($person, $tvShow);

        $this->view_data['selected_person'] = $person;
        $this->view_data['tvShow']          = $tvShow;

        return view('neptune::pro.tvShows.editNotes', $this->view_data);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param PersonTvShowsUpdateRequest $request
     */
    public function updateInfo(PersonTvShowsUpdateRequest $request, $person_slug, $tvShow_slug)
    {
        // get the person
        try
        {
            $person = Person::where('slug', $person_slug)->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Person not found',
                ]
            ]);
        }

        // throws ProExcpetion
        $this->personChecks($person);

        // get the tv show
        try
        {
            $tvShow = TvShow::where('slug', $tvShow_slug)->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Tv show was not found',
                ]
            ]);
        }

        // throws ProException
        $this->tvShowChecks($person, $tvShow);

        // manually set the editable fields
        // tv channel
        if($request->has('tv_channel') && ! empty($request->input('tv_channel')))
        {
            $tvShow->tv_channel_id = $request->input('tv_channel');
        }

        // synopsis
        if($request->has('synopsis') && ! empty($request->input('synopsis')))
        {
            $tvShow->translateOrNew('el')->synopsis = trim($request->input('synopsis'));
        }

        // ...we also want to tag the model fer moderation
        // ...(ideally on the condition that some data changed)
        if($tvShow->isDirty())
        {
            $tvShow->moderated = false;
        }

        $tvShow->save();

        // handle filmGenres
        // code to filter out the empty string value submitted because of the placeholder of the dropdown
        $genre_collection = new Collection($request->input('filmGenre'));
        $filtered_genre_collection = $genre_collection->filter(function ($value, $key) {
            return $value != '';
        });
        $tvShow->filmGenres()->sync($filtered_genre_collection);

        // handle own roles
        // firstly clear out any roles fer this tv show fer this person
        $existing_roles = PersonRoleTvShow::where('person_id', $person->id)
            ->where('tv_show_id', $tvShow->id)
            ->delete();

        // ...and then create the submitted roles
        foreach($request->input('own_role') as $role_id)
        {
            if( ! empty($role_id) )
            {
                // character handling for actor role
                if($role_id == 1 && $request->has('character') && ! empty($request->input('character')))
                {
                    $data = [
                        'role_id'       => $role_id,
                        'tv_show_id'    => $tvShow->id,
                        'person_id'     => $person->id,
                        'character'     => trim($request->input('character')),
                    ];
                }
                else
                {
                    $data = [
                        'role_id'       => $role_id,
                        'tv_show_id'    => $tvShow->id,
                        'person_id'     => $person->id,
                    ];
                }

                PersonRoleTvShow::create($data);

            }
        }

        return redirect()->route('neptune.pro.tvShows.editInfo', [$person->slug, $tvShow->slug])
            ->with('success', trans('neptune::pro/tvShows.edit_info.successful_update'));
    }

    /**
     * Show the form for finalising and publishing the pro person tvShow
     *
     * @param  $person_slug
     * @param  $tvShow_slug
     * @throws PersonProException
     */
    public function editPublished($person_slug, $tvShow_slug)
    {
        // get the person
        try
        {
            $person = Person::where('slug', $person_slug)->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Person not found',
                ]
            ]);
        }

        // throws ProExcpetion
        $this->personChecks($person);

        // get the tvShow
        try
        {
            $tvShow = TvShow::where('slug', $tvShow_slug)
                ->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'TV Show was not found',
                ]
            ]);
        }

        // throws ProException
        $this->tvShowChecks($person, $tvShow);

        $this->view_data['selected_person'] = $person;
        $this->view_data['tvShow']          = $tvShow;

        // check if the person has given his own role in the tv show
        $existing_roles = PersonRoleTvShow::where('person_id', $person->id)
            ->where('tv_show_id', $tvShow->id)
            ->get();

        if($existing_roles->isEmpty())
        {
            return view('neptune::pro.tvShows.editPublished', $this->view_data)
                ->withErrors(['own_role_status' => trans('neptune::pro/tvShows.edit_published.own_role_error_msg')]);
        }

        return view('neptune::pro.tvShows.editPublished', $this->view_data);
    }


    /**
     * Update the specified resource in storage.
     *
     */
    public function updatePublished(Request $request, $person_slug, $tvShow_slug)
    {
        // get the person
        try
        {
            $person = Person::where('slug', $person_slug)->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Person not found',
                ]
            ]);
        }

        // throws ProExcpetion
        $this->personChecks($person);

        // get the tv show
        try
        {
            $tvShow = TvShow::where('slug', $tvShow_slug)->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'TV Show was not found',
                ]
            ]);
        }

        // throws ProException
        $this->tvShowChecks($person, $tvShow);

        // check if the person has given his own role in the tv show
        $existing_roles = PersonRoleTvShow::where('person_id', $person->id)
            ->where('tv_show_id', $tvShow->id)
            ->get();

        if($existing_roles->isEmpty())
        {
            return redirect()->route('neptune.pro.tvShows.editPublished', [$person->slug, $tvShow->slug])
                ->withErrors(['own_role_status' => trans('neptune::pro/tvShows.edit_published.own_role_error_msg')]);
        }

        // manually set the fields
        $tvShow->moderated = true;
        $tvShow->published = true;

        $tvShow->save();

        try {
            // send notification email
            $input = [
                'person_id'         => $person->id,
                'person_name'       => $person->fullName,
                'person_slug'       => $person->slug,
                'user_id'           => $this->logged_user->id,
                'user_email'        => $this->logged_user->email,
                'endeavour_type'    => 'tv show',
                'endeavour_id'      => $tvShow->id,
                'endeavour_title'   => $tvShow->title,
                'endeavour_slug'    => $tvShow->slug,
            ];
            Mail::to(config('mail_addresses.contact'))->send(new ProPublishMessage($input));
        } catch (\Exception $exception) {
            report($exception);
        }

        return redirect()->route('neptune.pro.tvShows.index', [$person->slug, $tvShow->slug])
            ->with('success', trans('neptune::pro/tvShows.edit_published.successful_publish'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     */
    public function updateNotes(Request $request, $person_slug, $tvShow_slug)
    {
        // get the person
        try
        {
            $person = Person::where('slug', $person_slug)->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Person not found',
                ]
            ]);
        }

        // throws ProExcpetion
        $this->personChecks($person);

        // get the tv show
        try
        {
            $tvShow = TvShow::where('slug', $tvShow_slug)->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Tv show was not found',
                ]
            ]);
        }

        // throws ProException
        $this->tvShowChecks($person, $tvShow);

        // manually set the editable fields
        // user note
        if($request->has('user_notes') && ! empty($request->input('user_notes')))
        {
            $tvShow->user_notes = trim($request->input('user_notes'));
        }

        // ...we also want to tag the model fer moderation
        // ...(on the condition that some data changed)
        if($tvShow->isDirty())
        {
            $tvShow->moderated = false;
        }

        $tvShow->save();

        return redirect()->route('neptune.pro.tvShows.editNotes', [$person->slug, $tvShow->slug])
            ->with('success', trans('neptune::pro/tvShows.edit_notes.successful_update'));
    }


    /**
     * @param Person $person
     * @param TvShow $tvShow
     * @throws ProException
     */
    protected function tvShowChecks(Person $person, TvShow $tvShow)
    {
        // if the video does not have the given person connected, abort
//        if( ! $video->people()->get()->contains($person) )
//        {
//            throw new ProException('Video is not connected to ' . $person->fullName);
//        }

        // if the tvShow is not created by the logged in user, abort
        if( $this->logged_user->id != $tvShow->user_id )
        {
            throw new ProException('Insufficient permissions to edit the TV show');
        }

        // if the tv show is moderated, abort
        if( $tvShow->moderated )
        {
            throw new ProException('Insufficient permissions to edit the TV show');
        }

    }
}
