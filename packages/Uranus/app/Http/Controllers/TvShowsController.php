<?php namespace Packages\Uranus\app\Http\Controllers;

use App\Models\Tag;
use App\Models\TvShow;
use App\Models\FilmGenre;
use App\Models\TvChannel;
use App\Services\Search\Text\TextSearchService;
use Artesaos\SEOTools\Facades\SEOMeta;
use Illuminate\Http\Request;
use Packages\Uranus\app\Http\Requests\TvShowRequest;
use Packages\Uranus\app\Http\Requests\TvShowTranslatableRequest;

class TvShowsController extends Controller
{

    /**
     * @var TextSearchService
     */
    private $textSearch;

    public function __construct(TextSearchService $textSearch)
    {
        parent::__construct();

        $this->textSearch = $textSearch;
        $this->textSearch->setPreferredSearchLayer('eloquent');
    }

    /**
     * Display a listing of the resource.
     *
     */
    public function index(Request $request)
    {
        $this->authorizeForUser(auth('admin')->user(), 'see_plays');

        // if we have a query string then search plays by this string
        if ($request->has('q')) {
            $textSearchDTO = $this->textSearch
                ->setQuery($request->input('q'))
                ->setLimit(config('uranus.limits.plays.searchResults'))
                ->tvShows();
            $tvShows = $textSearchDTO->tvShows;
        }
        else {
            $sortBy = $request->input('sortBy', 'created_at');
            // Prevent sorting by translatable fields that no longer exist in main table
            if (in_array($sortBy, ['title', 'synopsis'])) {
                $sortBy = 'created_at';
            }
            $direction = $request->input('direction', 'desc');
            $page = $request->input('page', 1);
            $limit = config('uranus.limits.plays.index');
            $offset = ($page * $limit) - $limit;

            // Init
            $tvShows = TvShow::orderBy($sortBy, $direction);
            // If only non translated
            if($request->has('t') && $request->input('t') === 'notTranslated')
            {
                $tvShows = $tvShows->notTranslatedIn('en');
            }

            $tvShows = $tvShows
                ->offset($offset)
                ->limit($limit)
                ->paginate();

            $tvShows->setPath($request->url())
                ->appends('sortBy', $sortBy)
                ->appends('direction', $direction);
        }

        // Get all shows
        $allTvShows = TvShow::count();

        // Get not translated shows count
        $notTranslated = TvShow::notTranslatedIn('en')->count();

        // seo title
        SEOMeta::setTitle('Index Tv Shows');

        return view('uranus::tvShows.index', compact('tvShows', 'allTvShows', 'notTranslated'));
    }


    /**
     * Display a listing of the unfinalised resource.
     *
     */
    public function indexUnfinalised(Request $request)
    {
        $this->authorizeForUser(auth('admin')->user(), 'see_plays');

        $sortBy = $request->input('sortBy', 'created_at');
        // Prevent sorting by translatable fields that no longer exist in main table
        if (in_array($sortBy, ['title', 'synopsis'])) {
            $sortBy = 'created_at';
        }
        $direction = $request->input('direction', 'desc');
        $page = $request->input('page', 1);
        $limit = config('uranus.limits.plays.index');
        $offset = ($page * $limit) - $limit;

        // Init
        $tvShows = TvShow::orderBy($sortBy, $direction)
            ->where('finalised', 0);

        $tvShows = $tvShows
            ->offset($offset)
            ->limit($limit)
            ->paginate();

        $tvShows->setPath($request->url())
            ->appends('sortBy', $sortBy)
            ->appends('direction', $direction);

        // Get all shows
        $allTvShows = TvShow::count();

        // seo title
        SEOMeta::setTitle('Index unfinalised Tv Shows');

        return view('uranus::tvShows.index_unfinalised', compact('tvShows', 'allTvShows'));
    }


    /**
     * Show the list of user saved tv shows
     *
     * @param Request $request
     * @return mixed
     * @throws AuthorizationException
     */
    public function indexUserSaved(Request $request)
    {
        $this->authorizeForUser(auth('admin')->user(), 'see_plays');

        $sortBy = $request->input('sortBy', 'updated_at');
        // Prevent sorting by translatable fields that no longer exist in main table
        if (in_array($sortBy, ['title', 'synopsis'])) {
            $sortBy = 'updated_at';
        }
        $direction = $request->input('direction', 'desc');
        $page = $request->input('page', 1);
        $limit = config('uranus.limits.plays.index');
        $offset = ($page * $limit) - $limit;

        // Init
        $tvShows = TvShow::whereNotNull('user_id')
            ->orderBy($sortBy, $direction);

        // handle free text query string
        $query_q = $request->query('q');
        if ( !empty($query_q) )
        {
            $tvShows->where(function ($query) use ($query_q) {
                $query->orWhereHas('translations', function ($translationQuery) use ($query_q) {
                        $translationQuery->where('title', 'LIKE', '%' . $query_q . '%');
                    })
                    ->orWhere('notes', 'LIKE', '%' . $query_q . '%')
                    ->orWhere('user_notes', 'LIKE', '%' . $query_q . '%');
            });
        }

        // handle moderated query string
        $query_moderated = $request->query('moderated');
        if( !empty($query_moderated) )
        {
            if ($query_moderated == 'moderated')
            {
                $tvShows->where('moderated', true);
            }
            elseif ($query_moderated == 'unmoderated')
            {
                $tvShows->where('moderated', false);
            }
        }

        // handle published query string
        $query_published = $request->query('published');
        if( !empty($query_published) )
        {
            if ($query_published == 'published')
            {
                $tvShows->where('published', true);
            }
            elseif ($query_published == 'unpublished')
            {
                $tvShows->where('published', false);
            }
        }

        // handle user_id query string
        $query_user_id = $request->query('user_id');
        if( !empty($query_user_id) )
        {
            $tvShows->where('user_id', $query_user_id);
        }

        $tvShows = $tvShows
            ->offset($offset)
            ->limit($limit)
            ->paginate();

        $tvShows->setPath($request->url());

        // append q query string if present
        if ( !empty($query_q) )
        {
            $tvShows->appends('q', $query_q);
        }

        // append moderated query string if present
        if ( !empty($query_moderated) && ($query_moderated == 'moderated' || $query_moderated == 'unmoderated'))
        {
            $tvShows->appends('moderated', $query_moderated);
        }

        // append published query string if present
        if ( !empty($query_published) && ($query_published == 'published' || $query_published == 'unpublished'))
        {
            $tvShows->appends('published', $query_published);
        }

        // append user_id query string if present
        if ( !empty($query_user_id) )
        {
            $tvShows->appends('user_id', $query_user_id);
        }

        $tvShows->appends('sortBy', $sortBy)
            ->appends('direction', $direction);

        // load relations and extra data for all returned models
        $tvShows->each(function($tvShow) {
            $tvShow->load([
                'user',
            ]);
        });

        // seo title
        SEOMeta::setTitle('Index user saved tv shows');

        return view('uranus::tvShows.index_user_saved', compact('tvShows'));
    }


    /**
     * Show the form for creating a new resource.
     *
     */
    public function create()
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_plays');

        // List of shows for autocomplete suggestions
        $tvShows = TvShow::with('translations')->get();
        $tvShowTitles = [];
        foreach ($tvShows as $tvShow) {
            if ($tvShow->title) {
                $tvShowTitles[] = $tvShow->title;
            }
        }
        $tvShows = $tvShowTitles;

        // seo title
        SEOMeta::setTitle('New Tv Show');

        return view('uranus::tvShows.create', compact('tvShows'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param TvShowRequest $request
     * @throws AuthorizationException
     */
    public function store(TvShowRequest $request)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_plays');

        // Create TvShow with non-translatable fields first
        $tvShow = TvShow::create(['year' => trim($request->year)]);

        // Set translatable fields and save them first
        $tvShow->translateOrNew('el')->title = trim($request->title);
        $tvShow->save(); // This saves both the model and translations

        // Force regenerate slug now that translation is saved
        $tvShow->slug = null; // Clear existing slug
        $tvShow->save(); // Regenerate slug with proper title

        return redirect()->route('uranus.tvShows.edit', [$tvShow->id])
            ->with('success', 'Η σειρά με id ' . $tvShow->id . ' δημιουργήθηκε επιτυχώς');
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     * @throws AuthorizationException
     */
    public function edit($id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_plays');

        $tvShow = TvShow::where('id', $id)->with(['filmGenres', 'roles', 'user', 'translations'])->firstOrFail();
        $tvShow->load([
            'roles.tvShowPeople' => function ($query) use ($tvShow) {
                $query->wherePivot('tv_show_id', '=', $tvShow->id);
            },
        ]);
        $data['tvShow']     = $tvShow;
        $data['filmGenres'] = FilmGenre::all();
        $data['tvChannels'] = TvChannel::all();
        $data['tags']       = Tag::all();

        $rolesWithoutActors = [];
        // Workaround to get list of roles for which this movie
        // doesn't have any person
        $rolesIdsWithActors = [];
        foreach ($data['tvShow']->roles as $tvShowRole) {
            $rolesIdsWithActors[] = $tvShowRole->id;
        }
        foreach (config('roles')->where('for_tv', true) as $role) {
            if ( ! in_array($role->id, $rolesIdsWithActors)) {
                $rolesWithoutActors[] = $role;
            }
        }

        $data['rolesWithoutActors'] = $rolesWithoutActors;

        // seo title
        SEOMeta::setTitle('Edit ' . $data['tvShow']->title);

        // Images
        $data['resourceType']       = 'TvShow';
        $data['resourceTypeId']     = $id;

        return view('uranus::tvShows.edit', $data);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param TvShowRequest $request
     * @param int $id
     * @throws AuthorizationException
     */
    public function update(TvShowRequest $request, $id)
    {

        $this->authorizeForUser(auth('admin')->user(), 'manage_plays');

        $tvShow = TvShow::findOrFail($id);

        $input = [];

        // trim all request vars, except the arrays
        foreach ($request->all() as $attribute => $value)
        {
            if (is_string($value))
            {
                $input[$attribute] = trim($value);
            }
            elseif (is_null($value))
            {
                $input[$attribute] = '';
            }
            else
            {
                $input[$attribute] = $value;
            }
        }

        $input['published'] = isset($input['published']) ? 1 : 0;
        $input['finalised'] = isset($input['finalised']) ? 1 : 0;

        // Handle translatable fields separately
        if (isset($input['title'])) {
            $tvShow->translateOrNew('el')->title = $input['title'];
            unset($input['title']);
        }
        if (isset($input['synopsis'])) {
            $tvShow->translateOrNew('el')->synopsis = $input['synopsis'];
            unset($input['synopsis']);
        }

        // we update in two steps in order to check if an attribute has been changed
        $tvShow->fill($input);

        // and finaly save the model to persist tha changes
        $tvShow->save();

        $tvShow->filmGenres()->sync(($request->input('film_genre_list') ? : []));

        $tvShow->tags()->sync(($request->input('tag_list') ? : []));

        return redirect()->route('uranus.tvShows.edit', [$tvShow->id])
            ->with('success', 'Tv Show ' . $tvShow->id . ' updated');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param $id
     */
    public function destroy($id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'delete_plays');

        $deleted = TvSHow::destroy($id);

        $status = $deleted ? 1 : 0;
        $message = $deleted ? 'Η σειρά με id ' . $id . ' διαγράφηκε επιτυχώς' : 'Σφάλμα κατά την διαγραφή της σειράς';

        return response()->json([
            'status'  => $status,
            'message' => $message,
        ]);
    }

    /*
     * Used to return shows for use in select2 dropdowns input
     */
    public function getTvShowList(Request $request)
    {
        if ($request->has('searchTvShows'))
        {
            $paginator = $this->textSearch->setQuery($request->input('query'))->tvShows();

            $tvShows = [];
            if ($paginator->tvShows)
            {
                $tvShowsDTOS = $paginator->tvShows->all();
                foreach ($tvShowsDTOS as $dto)
                {
                    $tvShows[$dto->title . ' (' . $dto->year . ') (id: ' . $dto->id . ')'] = $dto->id;
                }
            }

            return $tvShows;
        }

    }

    /**
     * Show the form for editing the translatable attributes of the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function editTranslatable($id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_plays');

        $tvShow = TvShow::findOrFail($id);

        return view('uranus::tvShows.editTranslatable', compact('tvShow'));
    }

    /**
     * Update the translatable attributes of the specified resource in storage.
     *
     * @param  TvShowTranslatableRequest  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function updateTranslatable(TvShowTranslatableRequest $request, $id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_plays');

        $tvShow = TvShow::findOrFail($id);

        $input = [];

        // trim all request vars, except the arrays
        foreach ($request->all() as $attribute => $value)
        {
            if (is_string($value))
            {
                $input[$attribute] = trim($value);
            }
            elseif (is_null($value))
            {
                $input[$attribute] = '';
            }
            else
            {
                $input[$attribute] = $value;
            }
        }

        // Add translatable data
        $tvShow->translateOrNew('en')->title = $input['title_en'];
        $tvShow->translateOrNew('en')->synopsis = $input['synopsis_en'];

        // Save the model to persist the changes
        $tvShow->save();

        return redirect()->route('uranus.tvShows.translatable.edit', [$tvShow->id])
                         ->with('success', 'TV Show ' . $tvShow->id . ' translations updated');
    }

}
